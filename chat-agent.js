#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const { spawn } = require('child_process');
const { CNFullText } = require('./indexer.js');
const { JSDOM } = require('jsdom');

// 读取配置文件
const configPath = path.join(__dirname, 'config.json');
const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

// MCP服务器连接池
const mcpServers = {};
const mcpProcesses = {};

// 初始化MCP服务器连接
async function initializeMCPServers() {
    if (!config.mcp || !Array.isArray(config.mcp.servers)) {
        return;
    }
    
    for (const serverConfig of config.mcp.servers) {
        try {
            await connectToMCP(serverConfig);
        } catch (error) {
            console.error(`连接到MCP服务器失败: ${serverConfig.name || serverConfig.path}`, error.message);
        }
    }
}

// 连接到MCP服务器
async function connectToMCP(serverConfig) {
    console.log(`连接到MCP服务器: ${serverConfig.name || serverConfig.path}`);
    mcpServers[serverConfig.name || serverConfig.path] = {
        ...serverConfig,
        connected: true
    };
}

// 初始化MCP服务器连接
initializeMCPServers().catch(error => {
    console.error('初始化MCP服务器连接失败:', error);
});

// 处理相对路径
function resolvePath(relativeOrAbsolutePath) {
    if (path.isAbsolute(relativeOrAbsolutePath)) {
        return relativeOrAbsolutePath;
    } else {
        return path.join(__dirname, relativeOrAbsolutePath);
    }
}

// 解析配置中的路径
const documentPath = resolvePath(config.services.documentPath);
const fullIndexDbPath = resolvePath(config.services.fullIndexDbPath);

// 创建必要的目录
if (!fs.existsSync(documentPath)) {
    fs.mkdirSync(documentPath, { recursive: true });
}

// 程序启动后切换工作目录到documentPath
process.chdir(documentPath);

// 存储子目录索引引擎
const subDirectoryIndexes = {};

// 获取子目录对应的索引文件路径
function getSubDirectoryIndexPath(subDirName) {
    const indexDir = fullIndexDbPath;
    if (!fs.existsSync(indexDir)) {
        fs.mkdirSync(indexDir, { recursive: true });
    }
    return path.join(indexDir, `full_index_${subDirName}.bin`);
}

// 初始化全文索引（为所有子目录创建索引）
async function initializeFullIndex() {
    try {
        const items = fs.readdirSync(documentPath);
        const subDirectories = items.filter(item => {
            const itemPath = path.join(documentPath, item);
            return fs.statSync(itemPath).isDirectory();
        });

        if (subDirectories.length === 0) {
            const defaultIndexPath = getSubDirectoryIndexPath('default');
            const indexExists = fs.existsSync(defaultIndexPath);

            let indexEngine;
            if (!indexExists) {
                console.log('正在创建默认全文索引...');
                indexEngine = new CNFullText({
                    textField: 'content',
                    idField: 'file_path',
                    snippetWidth: 64,
                    mergeGap: 10
                });
                await buildFullIndexForDirectory(documentPath, indexEngine, 'default');
                indexEngine.save(defaultIndexPath);
                console.log('默认全文索引创建完成');
            } else {
                console.log('正在加载默认全文索引...');
                indexEngine = CNFullText.load(defaultIndexPath);
                console.log('默认全文索引加载完成');
            }

            subDirectoryIndexes['default'] = indexEngine;
            return;
        }

        for (const subDir of subDirectories) {
            const subDirPath = path.join(documentPath, subDir);
            const indexPath = getSubDirectoryIndexPath(subDir);
            const indexExists = fs.existsSync(indexPath);

            let indexEngine;
            if (!indexExists) {
                console.log(`正在创建子目录"${subDir}"的全文索引...`);
                indexEngine = new CNFullText({
                    textField: 'content',
                    idField: 'file_path',
                    snippetWidth: 64,
                    mergeGap: 10
                });
                await buildFullIndexForDirectory(subDirPath, indexEngine, subDir);
                indexEngine.save(indexPath);
                console.log(`子目录"${subDir}"全文索引创建完成`);
            } else {
                console.log(`正在加载子目录"${subDir}"全文索引...`);
                indexEngine = CNFullText.load(indexPath);
                console.log(`子目录"${subDir}"全文索引加载完成`);
            }

            subDirectoryIndexes[subDir] = indexEngine;
        }
    } catch (error) {
        console.error('初始化全文索引失败:', error);
        throw error;
    }
}

// 为特定目录构建全文索引
async function buildFullIndexForDirectory(dirPath, indexEngine, subDirName) {
    console.log(`正在扫描目录"${subDirName}"并建立索引...`);

    // 收集所有文档
    const documents = [];
    await scanAndCollectDocuments(dirPath, documents, subDirName);

    // 构建索引
    indexEngine.build(documents);

    console.log(`目录"${subDirName}"全文索引构建完成，共索引 ${documents.length} 个文档`);
}

// 递归扫描指定目录并收集文档
async function scanAndCollectDocuments(dirPath, documents, subDirName) {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);

        if (stat.isDirectory()) {
            await scanAndCollectDocuments(itemPath, documents, subDirName);
        } else if (stat.isFile()) {
            if (isTextFile(item)) {
                await collectDocument(itemPath, documents, subDirName);
            }
        }
    }
}

// 判断是否为文本文件
function isTextFile(filename) {
    const textExtensions = ['.txt', '.md', '.json', '.js', '.ts', '.html', '.css', '.xml', '.csv', '.log'];
    const ext = path.extname(filename).toLowerCase();
    return textExtensions.includes(ext) || /\.txt\.[0-9]+$/i.test(filename);
}

// 收集单个文档
async function collectDocument(filePath, documents, subDirName) {
    try {
        const content = fs.readFileSync(filePath, 'utf8');
        const fileName = path.basename(filePath);
        const relativePath = path.relative(path.join(documentPath, subDirName), filePath);

        documents.push({
            file_path: relativePath,
            file_name: fileName,
            content: content
        });
    } catch (error) {
        console.error(`收集文档 ${filePath} 时出错:`, error);
    }
}

// 创建readline接口用于命令行交互
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 对话历史和状态
let conversationHistory = [];

// 根据路径获取对应的索引引擎
function getIndexForPath(searchPath) {
    if (!searchPath) {
        return subDirectoryIndexes['default'] || null;
    }

    const normalizedPath = path.normalize(searchPath.replace(/^[\\\/]+|[\\\/]+$/g, ''));

    for (const subDir in subDirectoryIndexes) {
        if (subDir === 'default') continue;

        const subDirPath = path.normalize(subDir);
        if (normalizedPath.startsWith(subDirPath) || normalizedPath === subDirPath) {
            return subDirectoryIndexes[subDir];
        }
    }

    return subDirectoryIndexes['default'] || null;
}

// 根据路径获取对应的子目录名
function getSubDirNameForPath(searchPath) {
    if (!searchPath) {
        return 'default';
    }

    const normalizedPath = path.normalize(searchPath.replace(/^[\\\/]+|[\\\/]+$/g, ''));

    for (const subDir in subDirectoryIndexes) {
        if (subDir === 'default') continue;

        const subDirPath = path.normalize(subDir);
        if (normalizedPath.startsWith(subDirPath) || normalizedPath === subDirPath) {
            return subDir;
        }
    }

    return 'default';
}

// 修改后的全文索引搜索（使用CNFullText引擎）
async function fullIndexSearch(keywords, searchPath) {
    const indexEngine = getIndexForPath(searchPath);
    if (!indexEngine) {
        throw new Error('全文索引引擎未初始化');
    }

    if (!searchPath) {
        throw new Error('搜索路径是必需的');
    }

    // 获取对应的子目录名
    const subDirName = getSubDirNameForPath(searchPath);

    // 构建查询字符串
    const queryString = Array.isArray(keywords) ? keywords.join(' ') : keywords;

    // 执行搜索
    const searchResults = indexEngine.search(queryString, {
        topK: 50,
        requireAll: false,  // 使用OR逻辑
        snippetWidth: 64,
        mergeGap: 10
    });

    // 格式化结果
    const results = searchResults.map(result => {
        // 构建完整的文件路径（包含子目录名）
        const fullFilePath = subDirName === 'default'
            ? result.doc.file_path
            : path.join(subDirName, result.doc.file_path);

        let output = `文件: ${fullFilePath}`;
        if (result.snippets && result.snippets.length > 0) {
            result.snippets.forEach((snippet) => {
                const lineInfo = snippet.lines[0] === snippet.lines[1]
                    ? `第${snippet.lines[0]}行`
                    : `第${snippet.lines[0]}-${snippet.lines[1]}行`;
                output += `\n(${lineInfo}) ${snippet.snippet}`;
            });
        }
        return output;
    }).filter(result => result.trim() !== '');

    return results;
}

// 执行全文索引搜索工具
async function executeFullIndexSearch(params) {
    try {
        let keywordArray, searchPath;

        if (typeof params === 'object' && params !== null && params.keywords && params.path) {
            keywordArray = Array.isArray(params.keywords) ? params.keywords : [params.keywords];
            searchPath = params.path;
        } else {
            return { status: 400, data: `错误：full_index_search工具需要提供path参数来指定搜索目录` };
        }

        const results = await fullIndexSearch(keywordArray, searchPath);

        if (results.length > 0) {
            return { status: 200, data: `使用全文索引在目录"${searchPath}"中找到以下匹配项:\n${results.join('\n\n')}` };
        } else {
            return { status: 404, data: `全文索引在目录"${searchPath}"中未找到包含关键词"${keywordArray.join(', ')}"的内容` };
        }
    } catch (error) {
        return { status: 500, data: `全文索引搜索出错: ${error.message}` };
    }
}

// 从模板文件读取系统提示词
const systemPromptPath = path.join(__dirname, 'system-prompt.txt');
let systemPrompt = '';
if (fs.existsSync(systemPromptPath)) {
    systemPrompt = fs.readFileSync(systemPromptPath, 'utf8');
} else {
    console.error("错误：找不到系统提示词文件 'system-prompt.txt'");
    process.exit(1);
}

// 初始化对话历史
conversationHistory.push({
    role: "system",
    content: systemPrompt
});

// 只有在直接运行此文件时才执行主程序
if (require.main === module) {
    // 等待全文索引初始化完成后开始对话
    initializeFullIndex().then(() => {
        // 显示欢迎信息
        console.log("智能体对话系统 (命令行版本)");
        console.log("================================");
        console.log("请输入您的问题，或输入 'quit' 退出程序。");
        console.log("");

        // 开始对话
        promptUser();
    }).catch(error => {
        console.error('初始化全文索引失败:', error);
        process.exit(1);
    });
} else {
    initializeFullIndex().catch(error => {
        console.error('初始化全文索引失败:', error);
    });
}

// 提示用户输入
function promptUser() {
    rl.question("用户> ", (input) => {
        if (input.toLowerCase() === 'quit') {
            console.log("感谢使用智能体对话系统，再见！");
            rl.close();
            return;
        }
        
        if (input.trim()) {
            handleUserInput(input.trim());
        } else {
            promptUser();
        }
    });
}

// 处理用户输入
function handleUserInput(message) {
    // 添加用户消息到对话历史
    conversationHistory.push({
        role: 'user',
        content: message
    });
    
    console.log("");
    
    // 调用API
    callApi();
}

// 构建发送给API的消息历史
async function buildMessagesForApi(forceCompression = false) {
    const messages = [];
    
    // 添加系统提示词
    messages.push({
        role: 'system',
        content: systemPrompt
    });
    
    // 添加所有对话历史（跳过system消息，因为我们刚刚添加了它）
    conversationHistory.forEach(msg => {
        if (msg.role === 'user' || msg.role === 'assistant' || msg.role === 'tool') {
            messages.push({
                role: msg.role,
                content: msg.content
            });
        }
    });
    
    // 获取documentPath目录下的所有文件和目录，并添加到最新的用户消息中
    if (messages.length > 0 && messages[messages.length - 1].role === 'user') {
        const environmentDetail = getDocumentPathContents();
        messages[messages.length - 1].content += '\n\n' + environmentDetail;
    }
    
    // 管理上下文长度
    const managedMessages = await manageContext(messages, undefined, forceCompression);
    
    return managedMessages;
}

// 估算消息列表中的token数量
function estimateTokens(messages) {
    let totalTokens = 0;
    
    // 遍历所有消息，计算字符数
    for (const message of messages) {
        if (message.content) {
            // 对于中文文本，一个中文字符约等于一个token
            // 对于英文文本，仍然使用除以4的估算
            const chineseChars = (message.content.match(/[\u4e00-\u9fff]/g) || []).length;
            const otherChars = message.content.length - chineseChars;
            totalTokens += chineseChars + Math.ceil(otherChars / 4);
        }
    }
    
    return totalTokens;
}

// 管理上下文长度
async function manageContext(messages, maxTokens, forceCompression = false) {
    // 如果没有提供maxTokens，则从配置文件中读取
    if (maxTokens === undefined) {
        maxTokens = config.context && config.context.maxTokens ? config.context.maxTokens : 1280000;
    }
    
    // 估算当前消息的token数量
    let currentTokens = estimateTokens(messages);
    
    // 如果不强制压缩且当前token数量没有超出限制，直接返回
    if (!forceCompression && currentTokens <= maxTokens) {
        return messages;
    }

    // 创建一个新的消息数组，用于存储处理后的消息
    let processedMessages = [...messages];
    
    // 首先尝试去掉部分历史消息
    // 从最早的assistant和tool result消息开始去除
    while (currentTokens > maxTokens && processedMessages.length > 3) {  // 保留至少3条消息（system, latest user, latest assistant/tool）
        // 找到最早的一条assistant或tool消息
        let indexToRemove = -1;
        for (let i = 1; i < processedMessages.length - 1; i++) {  // 不考虑system消息和最新的用户消息
            if (processedMessages[i].role === 'assistant' || processedMessages[i].role === 'tool') {
                indexToRemove = i;
                break;
            }
        }
        
        // 如果没有找到assistant或tool消息，跳出循环
        if (indexToRemove === -1) {
            break;
        }
        
        // 去除消息
        processedMessages.splice(indexToRemove, 1);
        currentTokens = estimateTokens(processedMessages);
    }
    
    // 如果仍然超限，对tool result做相关性判断处理
    // 即使消息数量较少，也要处理tool result
    if (currentTokens > maxTokens) {
        // 从最后一条开始，按maxTokens大小分块
        for (let i = processedMessages.length - 1; i >= 0 && currentTokens > maxTokens; i--) {
            if (processedMessages[i].role === 'tool') {
                const originalContent = processedMessages[i].content;
                // 检查工具类型
                const toolMatch = processedMessages[i].content.match(/\[([a-z_]+).*?\]/i);
                const toolType = toolMatch ? toolMatch[1] : null;
                
                // 根据工具类型采用不同的chunk策略
                if (toolType === 'search_files' || toolType === 'full_index_search') {
                    // 对于search_files的结果，按文件边界切分
                    const chunks = [];
                    const fileSeparator = '\n文件: ';
                    let startIndex = 0;
                    
                    while (startIndex < originalContent.length) {
                        // 从chunkSize位置向后寻找最近的文件边界
                        let chunkEnd = Math.min(startIndex + maxTokens*2/3, originalContent.length);
                        
                        // 向后寻找文件边界
                        let nextFileBoundary = originalContent.indexOf(fileSeparator, chunkEnd);
                        if (nextFileBoundary !== -1) {
                            chunkEnd = nextFileBoundary;
                        }
                        
                        // 提取chunk
                        const chunk = originalContent.substring(startIndex, chunkEnd);
                        
                        // 如果chunk大于maxTokens，则抽取文件列表作为新chunk
                        if (chunk.length > maxTokens) {
                            // 抽取所有文件信息，组成文件列表
                            const filePattern = /文件: ([^\n]+)/g;
                            const fileMatches = chunk.match(filePattern);
                            if (fileMatches && fileMatches.length > 0) {
                                const fileList = fileMatches.join('\n');
                                chunks.push({
                                    content: `[文件列表]\n${fileList}`,
                                    isRelevant: true  // 直接判为相关chunk
                                });
                            } else {
                                chunks.push({
                                    content: chunk,
                                    isRelevant: null  // 需要调用模型判断
                                });
                            }
                        } else {
                            chunks.push({
                                content: chunk,
                                isRelevant: null  // 需要调用模型判断
                            });
                        }
                        
                        startIndex = chunkEnd;
                    }
                    
                    // 对chunks进行相关性判断
                    const relevantChunks = [];
                    for (const chunk of chunks) {
                        if (chunk.isRelevant === true) {
                            // 直接判为相关chunk
                            relevantChunks.push(chunk.content);
                        } else if (chunk.isRelevant === null) {
                            // 调用模型判断相关性
                            const userInputs = processedMessages.filter(msg => msg.role === 'user').map(msg => msg.content);
                            const allUserInput = userInputs.join(' ');
                            const isRelevant = await isChunkRelevantToUserInput(chunk.content, allUserInput);
                            if (isRelevant) {
                                relevantChunks.push(chunk.content);
                                break;  // 找到一个相关chunk就足够了
                            }
                        }
                    }
                    
                    // 如果有相关的内容，则保留相关的内容
                    if (relevantChunks.length > 0) {
                        processedMessages[i].content = relevantChunks.join('\n\n[内容块分隔]\n\n') + "\n\n[内容已根据相关性筛选]";
                    } else {
                        // 如果没有相关的内容，则保留原始内容的一部分
                        const charsToKeep = Math.min(originalContent.length, maxTokens * 4);
                        processedMessages[i].content = originalContent.substring(0, charsToKeep) + " [内容已截断]";
                    }
                } else if (toolType === 'read_file') {
                    // 对于read_file的结果，按换行符切分，且有1/3重叠
                    const chunks = [];
                    const chunkSize = maxTokens*2/3;
                    const overlap = Math.floor(chunkSize / 3);
                    
                    for (let j = 0; j < originalContent.length; j += chunkSize - overlap) {
                        // 从chunkSize位置向后寻找换行符
                        let chunkEnd = Math.min(j + chunkSize, originalContent.length);
                        
                        // 向后寻找换行符
                        let nextNewline = originalContent.indexOf('\n', chunkEnd);
                        if (nextNewline !== -1) {
                            chunkEnd = nextNewline + 1;  // 包含换行符
                        }
                        
                        // 提取chunk
                        const chunk = originalContent.substring(j, chunkEnd);
                        chunks.push(chunk);
                        
                        // 如果已经到达内容末尾，跳出循环
                        if (chunkEnd >= originalContent.length) {
                            break;
                        }
                    }
                    
                    // 获取所有用户输入
                    const userInputs = processedMessages.filter(msg => msg.role === 'user').map(msg => msg.content);
                    const allUserInput = userInputs.join(' ');
                    
                    // 对每块内容进行相关性判断
                    const relevantChunks = [];
                    for (const chunk of chunks) {
                        // 判断chunk是否与所有用户输入相关
                        const isRelevant = await isChunkRelevantToUserInput(chunk, allUserInput);
                        if (isRelevant) {
                            relevantChunks.push(chunk);
                            break;  // 找到一个相关chunk就足够了
                        }
                    }
                    
                    // 如果有相关的内容，则保留相关的内容
                    if (relevantChunks.length > 0) {
                        processedMessages[i].content = relevantChunks.join('\n\n[内容块分隔]\n\n') + "\n\n[内容已根据相关性筛选]";
                    } else {
                        // 如果没有相关的内容，则保留原始内容的一部分
                        const charsToKeep = Math.min(originalContent.length, maxTokens * 4);
                        processedMessages[i].content = originalContent.substring(0, charsToKeep) + " [内容已截断]";
                    }
                } else {
                    // 对于其他工具类型，使用原来的chunk策略
                    const chunkSize = maxTokens*2/3;
                    const chunks = [];
                    for (let j = 0; j < originalContent.length; j += chunkSize) {
                        chunks.push(originalContent.substring(j, j + chunkSize));
                    }
                    
                    // 获取所有用户输入
                    const userInputs = processedMessages.filter(msg => msg.role === 'user').map(msg => msg.content);
                    const allUserInput = userInputs.join(' ');
                    
                    // 对每块内容进行相关性判断
                    const relevantChunks = [];
                    for (const chunk of chunks) {
                        // 判断chunk是否与所有用户输入相关
                        const isRelevant = await isChunkRelevantToUserInput(chunk, allUserInput);
                        if (isRelevant) {
                            relevantChunks.push(chunk);
                            break;
                        }
                    }
                    
                    // 如果有相关的内容，则保留相关的内容
                    if (relevantChunks.length > 0) {
                        processedMessages[i].content = relevantChunks.join('\n\n[内容块分隔]\n\n') + "\n\n[内容已根据相关性筛选]";
                    } else {
                        // 如果没有相关的内容，则保留原始内容的一部分
                        const charsToKeep = Math.min(originalContent.length, maxTokens * 4);
                        processedMessages[i].content = originalContent.substring(0, charsToKeep) + " [内容已截断]";
                    }
                }
                currentTokens = estimateTokens(processedMessages);
            }
        }
    }
    
    return processedMessages;
}

// 使用模型判断chunk是否与用户输入相关
async function isChunkRelevantToUserInput(chunk, allUserInput) {
    try {
        const baseUrl = config.compressionApi.baseUrl;
        const modelId = config.compressionApi.modelId;
        const apiKey = config.compressionApi.apiKey;
        
        const prompt = `判断以下内容块是否与用户的问题相关。如果相关，回答"是"，否则回答"否"。\n\n内容块：${chunk}\n\n用户问题：${allUserInput}`;
        
        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: modelId,
                messages: [
                    {
                        role: 'system',
                        content: '你是一个相关性判断助手，能够判断给定的内容块是否与用户的问题相关。'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.0,
                max_tokens: 10
            })
        });
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        const answer = data.choices[0].message.content.trim().toLowerCase();
        return answer === '是' || answer.includes('是');
    } catch (error) {
        console.error('判断相关性时出错，假设内容是相关的:', error.message);
        return true;
    }
}

// 调用API (流式输出版本)
async function callApi(retryCount = 0) {
    try {
        const baseUrl = config.api.baseUrl;
        const modelId = config.api.modelId;
        const apiKey = config.api.apiKey;
        
        const messages = await buildMessagesForApi(retryCount > 0);
        
        const response = await fetch(`${baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                'Accept': 'text/event-stream'
            },
            body: JSON.stringify({
                model: modelId,
                messages: messages,
                temperature: 0.3,
                max_tokens: 4096,
                stream: true
            })
        });
        
        if (!response.ok) {
            process.stdout.write('\r\x1b[K');
            
            if (response.status === 400) {
                const currentTokens = estimateTokens(messages);
                console.log(`预估token数量: ${currentTokens}`);
                
                if (retryCount < 3) {
                    console.log(`API请求失败，正在进行第${retryCount + 1}次重试...`);
                    setTimeout(() => {
                        callApi(retryCount + 1);
                    }, 1000 * (retryCount + 1));
                    return;
                }
            }
            
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        process.stdout.write('\r\x1b[K');
      
        await handleStreamResponse(response);
    } catch (error) {
        console.log(`\n助手> 抱歉，处理您的请求时出现错误: ${error.message}`);
        promptUser();
    }
}

// 处理流式响应
async function handleStreamResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder('utf-8');
    
    let accumulatedContent = '';
    let hasStartedOutput = false;

    try {
        let streamEnded = false;

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\n').filter(line => line.trim() !== '');

            for (const line of lines) {
                if (line.startsWith('data: ')) {
                    const data = line.slice(6);
                    if (data === '[DONE]') {
                        streamEnded = true;
                        break;
                    }

                    try {
                        const parsed = JSON.parse(data);
                        const content = parsed.choices[0]?.delta?.content || '';
                        if (content) {
                            if (!hasStartedOutput) {
                                process.stdout.write("助手> ");
                                hasStartedOutput = true;
                            }

                            accumulatedContent += content;
                            process.stdout.write(content);
                        }
                    } catch (e) {
                        // 忽略解析错误
                    }
                }
            }

            if (streamEnded) {
                break;
            }
        }
        
        conversationHistory.push({
            role: 'assistant',
            content: accumulatedContent
        });
        
        console.log("");
        
        setTimeout(() => {
            parseAndExecuteTool(accumulatedContent);
        }, 100);
    } catch (error) {
        console.error('流式响应处理错误:', error);
        console.log(`\n助手> 流式响应处理错误: ${error.message}`);
        promptUser();
    } finally {
        reader.releaseLock();
    }
}

// 处理API响应
function handleApiResponse(data) {
    const assistantMessage = data.choices[0].message.content;
    
    console.log(`助手> ${assistantMessage}`);
    
    conversationHistory.push({
        role: 'assistant',
        content: assistantMessage
    });
    
    parseAndExecuteTool(assistantMessage);
}

// 解析并执行工具
function parseAndExecuteTool(toolCall, retryCount = 0) {
    try {
        if (toolCall.includes('<回答问题>') && !toolCall.includes('</回答问题>')) {
            if (retryCount < 5) {
                setTimeout(() => {
                    parseAndExecuteTool(toolCall, retryCount + 1);
                }, 200);
                return;
            }
        }

        const toolInfo = parseToolCall(toolCall);

        if (!toolInfo) {
            if (retryCount < 3) {
                console.log(`工具执行结果> 错误：必须调用工具，正在重新请求模型... (第${retryCount + 1}次重试)`);
                setTimeout(() => {
                    callApi();
                }, 1000);
            } else {
                console.log("工具执行结果> 错误：必须调用工具，已达到最大重试次数");
                promptUser();
            }
            return;
        }
        
        if (toolInfo.name === 'search_files') {
            const searchInfo = parseSearchFilesInfo(toolInfo.fullContent);
            if (searchInfo.filePattern) {
                console.log(`工具调用> ${toolInfo.name}: 在"${searchInfo.path}"中搜索"${searchInfo.regex}"，文件模式"${searchInfo.filePattern}"`);
            } else {
                console.log(`工具调用> ${toolInfo.name}: 在"${searchInfo.path}"中搜索"${searchInfo.regex}"`);
            }
        } else if (toolInfo.name === '回答问题') {
            const answerPreview = toolInfo.paramValue && toolInfo.paramValue.answer 
                ? toolInfo.paramValue.answer.substring(0, 10) + (toolInfo.paramValue.answer.length > 10 ? '...' : '')
                : '无回答内容';
            console.log(`工具调用> ${toolInfo.name}: ${answerPreview}`);
        } else {
            const paramPreview = toolInfo.paramValue 
                ? (typeof toolInfo.paramValue === 'string' ? toolInfo.paramValue : JSON.stringify(toolInfo.paramValue))
                : '无参数';
            console.log(`工具调用> ${toolInfo.name}: ${paramPreview}`);
        }
        
        executeTool(toolInfo);
    } catch (error) {
        if (retryCount < 3) {
            console.log(`工具执行结果> 工具解析错误: ${error.message}，正在重新请求模型... (第${retryCount + 1}次重试)`);
            setTimeout(() => {
                callApi();
            }, 1000);
        } else {
            console.log(`工具执行结果> 工具解析错误: ${error.message}，已达到最大重试次数`);
            promptUser();
        }
    }
}

// 解析search_files工具的信息
function parseSearchFilesInfo(toolContent) {
    try {
        const pathPattern = /<path>([\s\S]*?)<\/path>/i;
        const regexPattern = /<regex>([\s\S]*?)<\/regex>/i;
        const filePatternPattern = /<file_pattern>([\s\S]*?)<\/file_pattern>/i;
        
        const pathMatch = toolContent.match(pathPattern);
        const regexMatch = toolContent.match(regexPattern);
        const filePatternMatch = toolContent.match(filePatternPattern);
        
        return {
            path: pathMatch ? pathMatch[1].trim() : '未知路径',
            regex: regexMatch ? regexMatch[1].trim() : '未知关键词',
            filePattern: filePatternMatch ? filePatternMatch[1].trim() : null
        };
    } catch (error) {
        return {
            path: '解析错误',
            regex: '解析错误',
            filePattern: '解析错误'
        };
    }
}

// 解析工具调用
function parseToolCall(toolCall) {
    const tools = [
        { name: '提问', tag: '提问', paramTag: 'question' },
        { name: '查资料', tag: '查资料', paramTag: 'keywords' },
        { name: 'list_files', tag: 'list_files', paramTag: 'path' },
        { name: 'read_file', tag: 'read_file', paramTag: null },
        { name: 'search_files', tag: 'search_files', paramTag: null },
        { name: 'full_index_search', tag: 'full_index_search', paramTag: null },
        { name: '转人工', tag: '转人工', paramTag: null },
        { name: '回答问题', tag: '回答问题', paramTag: 'answer' },
        { name: 'fetch_web', tag: 'fetch_web', paramTag: 'url' }
    ];
    
    for (const tool of tools) {
        let toolContent = null;
        let matches = null;

        let pattern = new RegExp(`<${tool.tag}>[\\s\\S]*?</${tool.tag}>`, 'g');
        matches = toolCall.match(pattern);

        if (matches && matches.length > 0) {
            toolContent = matches[0];
        } else {
            pattern = new RegExp(`<${tool.tag}>([\\s\\S]*)$`, 'i');
            const match = toolCall.match(pattern);
            if (match) {
                toolContent = `<${tool.tag}>${match[1]}`;
            }
        }

        if (toolContent) {
            let paramValue = null;
            
            if (tool.paramTag) {
                if (tool.paramTag === 'keywords') {
                    paramValue = [];
                    let paramPattern = new RegExp(`<${tool.paramTag}>([\\s\\S]*?)</${tool.paramTag}>`, 'gi');
                    let paramMatches = toolContent.matchAll(paramPattern);
                    for (const match of paramMatches) {
                        paramValue.push(match[1].trim());
                    }
                } else if (tool.paramTag === 'answer') {
                    let answer = null;
                    let answerPattern = new RegExp(`<answer>([\\s\\S]*?)</answer>`, 'i');
                    let answerMatch = toolContent.match(answerPattern);
                    if (answerMatch) {
                        answer = answerMatch[1].trim();
                    } else {
                        answerPattern = new RegExp(`answer>([\\s\\S]*?)</answer>`, 'i');
                        answerMatch = toolContent.match(answerPattern);
                        if (answerMatch) {
                            answer = answerMatch[1].trim();
                        } else {
                            answerPattern = new RegExp(`<answer>([\\s\\S]*?)(?=<reference|$)`, 'i');
                            answerMatch = toolContent.match(answerPattern);
                            if (answerMatch) {
                                answer = answerMatch[1].trim();
                            } else {
                                const contentAfterTag = toolContent.replace(new RegExp(`^<${tool.tag}>\\s*`, 'i'), '');
                                if (contentAfterTag && !contentAfterTag.includes('<reference')) {
                                    answer = contentAfterTag.trim();
                                }
                            }
                        }
                    }

                    const references = [];
                    let referencePattern = new RegExp(`<reference>[\\s\\S]*?<path>([\\s\\S]*?)</path>[\\s\\S]*?<name>([\\s\\S]*?)</name>[\\s\\S]*?</reference>`, 'gi');
                    let referenceMatches = toolContent.matchAll(referencePattern);
                    for (const match of referenceMatches) {
                        references.push({
                            path: match[1].trim(),
                            name: match[2].trim()
                        });
                    }

                    paramValue = {
                        answer: answer,
                        references: references
                    };
                } else {
                    let paramPattern = new RegExp(`<${tool.paramTag}>([\\s\\S]*?)</${tool.paramTag}>`, 'i');
                    let paramMatch = toolContent.match(paramPattern);
                    if (paramMatch) {
                        paramValue = paramMatch[1].trim();
                    } else {
                        paramPattern = new RegExp(`${tool.paramTag}>([\\s\\S]*?)</${tool.paramTag}>`, 'i');
                        paramMatch = toolContent.match(paramPattern);
                        if (paramMatch) {
                            paramValue = paramMatch[1].trim();
                        } else {
                            paramPattern = new RegExp(`<${tool.paramTag}>([\\s\\S]*?)(?=<[a-zA-Z_]+|</${tool.tag}>|$)`, 'i');
                            paramMatch = toolContent.match(paramPattern);
                            if (paramMatch) {
                                paramValue = paramMatch[1].trim();
                            } else {
                                paramPattern = new RegExp(`${tool.paramTag}>([\\s\\S]*?)(?=<[a-zA-Z_]+|</${tool.tag}>|$)`, 'i');
                                paramMatch = toolContent.match(paramPattern);
                                if (paramMatch) {
                                    paramValue = paramMatch[1].trim();
                                } else {
                                    const contentAfterTag = toolContent.replace(new RegExp(`^<${tool.tag}>\\s*`, 'i'), '');
                                    if (contentAfterTag && !contentAfterTag.includes('<')) {
                                        paramValue = contentAfterTag.trim();
                                    }
                                }
                            }
                        }
                    }
                }
            } else if (tool.name === 'read_file') {
                let pathValue = null;
                let lineValue = null;

                let pathPattern = new RegExp(`<path>([\\s\\S]*?)</path>`, 'i');
                let pathMatch = toolContent.match(pathPattern);
                if (pathMatch) {
                    pathValue = pathMatch[1].trim();
                } else {
                    pathPattern = new RegExp(`path>([\\s\\S]*?)</path>`, 'i');
                    pathMatch = toolContent.match(pathPattern);
                    if (pathMatch) {
                        pathValue = pathMatch[1].trim();
                    } else {
                        pathPattern = new RegExp(`<path>([\\s\\S]*?)(?=<line|</${tool.tag}>|$)`, 'i');
                        pathMatch = toolContent.match(pathPattern);
                        if (pathMatch) {
                            pathValue = pathMatch[1].trim();
                        }
                    }
                }

                let linePattern = new RegExp(`<line>([\\s\\S]*?)</line>`, 'i');
                let lineMatch = toolContent.match(linePattern);
                if (lineMatch) {
                    lineValue = lineMatch[1].trim();
                } else {
                    linePattern = new RegExp(`line>([\\s\\S]*?)</line>`, 'i');
                    lineMatch = toolContent.match(linePattern);
                    if (lineMatch) {
                        lineValue = lineMatch[1].trim();
                    } else {
                        linePattern = new RegExp(`<line>([\\s\\S]*?)(?=</${tool.tag}>|$)`, 'i');
                        lineMatch = toolContent.match(linePattern);
                        if (lineMatch) {
                            lineValue = lineMatch[1].trim();
                        }
                    }
                }

                if (pathValue) {
                    paramValue = {
                        path: pathValue,
                        line: lineValue
                    };
                }
            } else if (tool.name === 'full_index_search') {
                let keywordsValue = [];
                let pathValue = null;

                let keywordsPattern = new RegExp(`<keywords>([\\s\\S]*?)</keywords>`, 'gi');
                let keywordsMatches = toolContent.matchAll(keywordsPattern);
                for (const match of keywordsMatches) {
                    keywordsValue.push(match[1].trim());
                }

                if (keywordsValue.length === 0) {
                    keywordsPattern = new RegExp(`keywords>([\\s\\S]*?)</keywords>`, 'gi');
                    keywordsMatches = toolContent.matchAll(keywordsPattern);
                    for (const match of keywordsMatches) {
                        keywordsValue.push(match[1].trim());
                    }
                }

                let pathPattern = new RegExp(`<path>([\\s\\S]*?)</path>`, 'i');
                let pathMatch = toolContent.match(pathPattern);
                if (pathMatch) {
                    pathValue = pathMatch[1].trim();
                } else {
                    pathPattern = new RegExp(`path>([\\s\\S]*?)</path>`, 'i');
                    pathMatch = toolContent.match(pathPattern);
                    if (pathMatch) {
                        pathValue = pathMatch[1].trim();
                    } else {
                        pathPattern = new RegExp(`<path>([\\s\\S]*?)(?=<keywords|$)`, 'i');
                        pathMatch = toolContent.match(pathPattern);
                        if (pathMatch) {
                            pathValue = pathMatch[1].trim();
                        }
                    }
                }

                if (keywordsValue.length > 0 && pathValue) {
                    paramValue = {
                        keywords: keywordsValue,
                        path: pathValue
                    };
                }
            }
            
            return {
                name: tool.name,
                tag: tool.tag,
                paramTag: tool.paramTag,
                paramValue: paramValue,
                fullContent: toolContent
            };
        }
    }
    
    return null;
}

// 执行工具
async function executeTool(toolInfo) {
    // 对于提问、回答问题、转人工这三个工具，直接输出对应的内容
    if (toolInfo.name === '提问') {
        console.log(`助手> ${toolInfo.paramValue}`);
        console.log("");
        promptUser();
        return;
    } else if (toolInfo.name === '回答问题') {
        // 显示回答内容
        console.log(`助手> ${toolInfo.paramValue.answer}`);
        
        // 显示引用的文件
        if (toolInfo.paramValue.references && toolInfo.paramValue.references.length > 0) {
            console.log("");
            console.log("参考文件:");
            for (const reference of toolInfo.paramValue.references) {
                if (reference.name && reference.path) {
                    // 确保显示的是相对路径
                    const relativePath = path.relative(__dirname, reference.path);
                    console.log(`- ${reference.name}: ${relativePath}`);
                } else if (reference.path) {
                    // 确保显示的是相对路径
                    const relativePath = path.relative(__dirname, reference.path);
                    console.log(`- ${relativePath}`);
                }
            }
        }
        
        console.log("");
        promptUser();
        return;
    } else if (toolInfo.name === '转人工') {
        console.log("助手> 正在为您转接人工客服...");
        console.log("");
        promptUser();
        return;
    }
    
    let result = "";
    
    try {
        switch (toolInfo.name) {
            case '查资料':
                result = searchDocuments(toolInfo.paramValue);
                break;
                
            case 'list_files':
                result = await listFiles(toolInfo.paramValue);
                break;
                
            case 'read_file':
                if (typeof toolInfo.paramValue === 'object' && toolInfo.paramValue !== null) {
                    result = readFile(toolInfo.paramValue.path, toolInfo.paramValue.line);
                } else {
                    result = readFile(toolInfo.paramValue);
                }
                break;
                
            case 'search_files':
                result = searchFiles(toolInfo.fullContent);
                break;

            case 'full_index_search':
                result = await executeFullIndexSearch(toolInfo.paramValue);
                break;
                
            case 'fetch_web':
                result = await fetchWebPage(toolInfo.paramValue);
                break;

            default:
                result = "未知工具";
        }
    } catch (error) {
        result = { status: 500, data: `工具执行出错: ${error.message}` };
    }
    
    // 检查指定工具的执行状态并打印日志
    if (['list_files', 'read_file', 'search_files', 'full_index_search', 'fetch_web'].includes(toolInfo.name)) {
        // 检查结果是否包含状态码
        if (result && typeof result === 'object' && result.status !== undefined) {
            // 根据状态码打印执行结果
            switch (result.status) {
                case 200:
                    console.log(`工具执行结果> ${toolInfo.name} 执行成功`);
                    break;
                case 400:
                    console.log(`工具执行结果> ${toolInfo.name} 参数错误: ${result.data}`);
                    break;
                case 404:
                    console.log(`工具执行结果> ${toolInfo.name} 未找到: ${result.data}`);
                    break;
                case 500:
                    console.log(`工具执行结果> ${toolInfo.name} 执行失败: ${result.data}`);
                    break;
                default:
                    console.log(`工具执行结果> ${toolInfo.name} 执行完成，状态码: ${result.status}`);
                    break;
            }
        } else {
            // 如果结果不包含状态码，直接打印结果
            console.log(`工具执行结果> ${toolInfo.name} 执行结果: ${result}`);
        }
    }

    // 检查是否需要继续对话
    // 添加工具结果到对话历史
    conversationHistory.push({
        role: 'tool',
        content: `[${toolInfo.name} ${toolInfo.paramValue || ''}] result: ${typeof result === 'object' ? JSON.stringify(result) : result}`
    });
    
    // 继续调用API
    setTimeout(() => {
        callApi();
    }, 1000);
}

const puppeteer = require('puppeteer-core');

// 使用puppeteer-core获取网页内容
async function fetchWebPage(url) {
    let browser;
    try {
        console.log(`开始获取网页: ${url}`);
        
        // 使用系统安装的Chrome
        browser = await puppeteer.launch({
            executablePath: 'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            headless: 'new',
            args: [
                '--disable-gpu',
                '--disable-dev-shm-usage',
                '--disable-setuid-sandbox',
                '--no-sandbox'
            ]
        });

        const page = await browser.newPage();
        
        // 设置用户代理和视口
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36');
        await page.setViewport({ width: 1280, height: 800 });
        
        // 导航到页面并等待网络空闲
        await page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // 检查重定向
        const finalUrl = page.url();
        if (finalUrl !== url && finalUrl.includes('kb.qianxin.com')) {
            console.log(`检测到重定向到: ${finalUrl}`);
            return fetchWebPage(finalUrl);
        }

        // 尝试从常见内容区域提取文本
        const contentSelectors = [
            '.article-content', 
            '.content', 
            '.main-content',
            '#content',
            '.detail-content',
            'article'
        ];
        
        let content = '';
        for (const selector of contentSelectors) {
            const element = await page.$(selector);
            if (element) {
                content = await page.evaluate(el => el.textContent, element);
                if (content.trim().length > 0) break;
            }
        }
        
        // 如果没有找到特定内容区域，获取整个页面内容
        if (!content.trim()) {
            content = await page.evaluate(() => document.body.textContent);
        }
        
        console.log(`提取到文本内容，长度: ${content.length}字符`);
        
        // 清理多余空白和换行
        content = content.replace(/\s+/g, ' ').trim();
        
        if (!content) {
            return { status: 500, data: '获取网页内容失败: 无法提取有效文本内容' };
        }
        
        // 截取前64000个字符避免返回过多内容
        const result = content.substring(0, 64000) + (content.length > 64000 ? '...' : '');
        console.log(`最终返回内容长度: ${result.length}字符`);
        return { status: 200, data: result };
    } catch (error) {
        console.error(`获取网页内容失败: ${error.message}`);
        return { status: 500, data: `获取网页内容失败: ${error.message}` };
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// 执行MCP工具
async function executeMcpTool(toolInfo) {
    // 检查MCP服务器是否存在
    if (!mcpServers[toolInfo.serverName]) {
        return `错误：MCP服务器"${toolInfo.serverName}"未连接`;
    }
    
    // 检查MCP服务器是否已连接
    if (!mcpServers[toolInfo.serverName].connected) {
        return `错误：MCP服务器"${toolInfo.serverName}"未连接`;
    }
    
    // 这里应该实现实际的MCP工具执行逻辑
    // 由于MCP协议的具体实现比较复杂，我们在这里只做占位
    console.log(`执行MCP工具: ${toolInfo.serverName}:${toolInfo.tag}`);
    
    // 模拟工具执行结果
    return `MCP工具"${toolInfo.serverName}:${toolInfo.tag}"执行结果`;
}

// 查资料功能
function searchDocuments(keywords) {
    const keywordArray = Array.isArray(keywords) ? keywords : [keywords];
    let results = [];
    
    // 只搜索documents目录下的所有文件
    const searchDirs = [documentPath];
    
    for (const dir of searchDirs) {
        if (fs.existsSync(dir)) {
            const files = fs.readdirSync(dir);
            for (const file of files) {
                if (file.endsWith('.txt')) {
                    const filePath = path.join(dir, file);
                    const content = fs.readFileSync(filePath, 'utf8');
                    
                    // 检查是否包含任何关键词
                    let match = false;
                    for (const keyword of keywordArray) {
                        if (content.includes(keyword)) {
                            match = true;
                            break;
                        }
                    }
                    
                    if (match) {
                        results.push(`文件: ${file}\n内容预览: ${content.substring(0, 200)}...`);
                    }
                }
            }
        }
    }
    
    if (results.length > 0) {
        return `找到以下相关资料:\n${results.join('\n\n')}`;
    } else {
        // 优化关键词重试逻辑
        const optimizedKeywords = keywordArray.map(keyword => {
            // 简单的关键词优化：添加相关词
            if (keyword.includes('故障')) {
                return [keyword, '问题', '解决'];
            } else if (keyword.includes('设备')) {
                return [keyword, '产品', '型号'];
            }
            return [keyword];
        }).flat();
        
        // 用优化后的关键词再次搜索
        for (const dir of searchDirs) {
            if (fs.existsSync(dir)) {
                const files = fs.readdirSync(dir);
                for (const file of files) {
                    if (file.endsWith('.txt')) {
                        const filePath = path.join(dir, file);
                        const content = fs.readFileSync(filePath, 'utf8');
                        
                        // 检查是否包含优化后的关键词
                        let match = false;
                        for (const keyword of optimizedKeywords) {
                            if (content.includes(keyword)) {
                                match = true;
                                break;
                            }
                        }
                        
                        if (match) {
                            results.push(`文件: ${file}\n内容预览: ${content.substring(0, 200)}...`);
                        }
                    }
                }
            }
        }
        
        if (results.length > 0) {
            return `通过优化关键词找到以下相关资料:\n${results.join('\n\n')}`;
        } else {
            return `未找到包含关键词"${keywordArray.join(', ')}"的资料`;
        }
    }
}

// list_files功能
function listFiles(params) {
    // 解析参数
    let pathParam, recursive = false;
    
    if (typeof params === 'string') {
        // 如果参数是字符串，将其视为路径
        pathParam = params;
    } else if (typeof params === 'object' && params !== null) {
        // 如果参数是对象，解析path和recursive属性
        pathParam = params.path || '.';
        recursive = params.recursive || false;
    } else {
        pathParam = '.';
    }
    
    let dirPath = pathParam;
    
    // 处理相对路径
    if (!path.isAbsolute(dirPath)) {
        // 由于工作目录已切换到documentPath，直接使用相对路径
        dirPath = path.join(documentPath, dirPath);
    }
    
    if (!fs.existsSync(dirPath)) {
        return { status: 404, data: `目录"${dirPath}"不存在，请更换目录` };
    }
    
    try {
        if (recursive) {
            // 递归列出目录
            const result = listDirectoryRecursive(dirPath, 0);
            return { status: 200, data: result };
        } else {
            // 只列出当前目录
            const items = fs.readdirSync(dirPath);
            if (items.length === 0) {
                return { status: 200, data: `目录"${dirPath}"为空` };
            } else {
                return { status: 200, data: `目录"${dirPath}"包含以下文件:\n${items.map(item => `- ${item}`).join('\n')}` };
            }
        }
    } catch (error) {
        return { status: 500, data: `读取目录"${dirPath}"时出错: ${error.message}` };
    }
}

// 递归列出目录内容的辅助函数
function listDirectoryRecursive(dirPath, depth) {
    let result = '';
    
    try {
        const items = fs.readdirSync(dirPath);
        
        // 分离目录和文件
        const directories = [];
        const files = [];
        
        for (const item of items) {
            const itemPath = path.join(dirPath, item);
            const stat = fs.statSync(itemPath);
            
            if (stat.isDirectory()) {
                directories.push(item);
            } else if (stat.isFile()) {
                files.push(item);
            }
        }
        
        // 添加目录
        directories.sort().forEach(dir => {
            const dirPathFull = path.join(dirPath, dir);
            result += `${'  '.repeat(depth)}- ${dir}/\n`;
            // 递归获取子目录结构
            result += listDirectoryRecursive(dirPathFull, depth + 1);
        });
        
        // 添加文件
        files.sort().forEach(file => {
            result += `${'  '.repeat(depth)}- ${file}\n`;
        });
        
        return result;
    } catch (error) {
        return `无法读取目录"${dirPath}": ${error.message}`;
    }
}

// read_file功能
function readFile(location, lineRange) {
    // 支持绝对路径和相对路径
    let filePath = location;
    
    // 如果是相对路径，使用统一的路径处理逻辑
    if (!path.isAbsolute(location)) {
        // 由于工作目录已切换到documentPath，直接使用相对路径
        filePath = path.join(process.cwd(), location);
    }
    
    // 检查文件是否存在
    if (fs.existsSync(filePath)) {
        // 检查是否为目录
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            // 如果是目录，列出目录内容
            try {
                const items = fs.readdirSync(filePath);
                if (items.length === 0) {
                    return { status: 200, data: `目录"${filePath}"为空` };
                } else {
                    return { status: 200, data: `目录"${filePath}"包含以下文件和目录:\n${items.map(item => `- ${item}`).join('\n')}` };
                }
            } catch (error) {
                return { status: 500, data: `无法读取目录"${filePath}": ${error.message}` };
            }
        }
        
        // 如果是文件，读取文件内容
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            
            // 如果没有指定行号范围，且文件内容超过max_tokens*2/3
            if (!lineRange) {
                const maxChars = Math.floor((config.context?.maxTokens || 32000) * 2 / 3);
                if (content.length > maxChars) {
                    const lines = content.split('\n');
                    let partialContent = '';
                    let lineCount = 0;
                    let charCount = 0;
                    
                    // 计算可以读取多少行
                    for (let i = 0; i < lines.length; i++) {
                        if (charCount + lines[i].length > maxChars) {
                            break;
                        }
                        partialContent += lines[i] + '\n';
                        charCount += lines[i].length + 1;
                        lineCount++;
                    }
                    
                    const remainingLines = lines.length - lineCount;
                    return { status: 200, data: `文件"${filePath}"内容过大，共${lines.length}行，已读取1-${lineCount}行，还剩${lineCount+1}-${lineCount + remainingLines}行:\n${partialContent}` };
                }
            }
            
            // 如果指定了行号范围，只返回指定范围的内容
            if (lineRange) {
                const lines = content.split('\n');
                const [start, end] = lineRange.split('-').map(num => parseInt(num.trim(), 10));
                
                if (!isNaN(start) && !isNaN(end) && start > 0 && end > 0 && start <= end) {
                    const startIndex = start - 1; // 转换为0-based索引
                    const endIndex = Math.min(end, lines.length);
                    const selectedLines = lines.slice(startIndex, endIndex);
                    
                    // 为每行添加行号
                    const numberedLines = selectedLines.map((line, index) => `${startIndex + index + 1}: ${line}`);
                    content = numberedLines.join('\n');
                    return { status: 200, data: `文件"${filePath}"的第${start}-${end}行内容:\n${content}` };
                } else if (!isNaN(start) && start > 0) {
                    // 如果只指定了起始行
                    const startIndex = start - 1;
                    const selectedLines = lines.slice(startIndex);
                    
                    content = selectedLines.join('\n');
                    return { status: 200, data: `文件"${filePath}"的第${start}行及之后内容:\n${content}` };
                }
            }
            
            return { status: 200, data: `文件"${filePath}"的内容:\n${content}` };
        } catch (error) {
            return { status: 500, data: `读取文件"${filePath}"时出错: ${error.message}` };
        }
    } else {
        return { status: 404, data: `文件"${location}"不存在, 请更换正确的文件名称和路径` };
    }
}

// search_files工具功能
function searchFiles(toolContent) {
    try {
        // 解析search_files工具的参数
        const pathPattern = /<path>([\s\S]*?)<\/path>/i;
        const regexPattern = /<regex>([\s\S]*?)<\/regex>/i;
        const filePatternPattern = /<file_pattern>([\s\S]*?)<\/file_pattern>/i;
        
        const pathMatch = toolContent.match(pathPattern);
        const regexMatch = toolContent.match(regexPattern);
        const filePatternMatch = toolContent.match(filePatternPattern);
        
        if (!pathMatch || !regexMatch) {
            return { status: 400, data: "错误：search_files工具需要path和regex参数" };
        }
        
        let searchPath = pathMatch[1].trim();
        const regexStr = regexMatch[1].trim();
        const filePattern = filePatternMatch ? filePatternMatch[1].trim() : null;
        
        // 编译正则表达式 (使用Rust正则表达式语法，JavaScript正则表达式语法与Rust类似)
        let regex;
        try {
            regex = new RegExp(regexStr, 'gi'); // 添加忽略大小写标志以获得更好的召回
        } catch (e) {
            return { status: 400, data: `错误：无效的正则表达式"${regexStr}"` };
        }
        
        // 处理相对路径
        if (!path.isAbsolute(searchPath)) {
            // 由于工作目录已切换到documentPath，直接使用相对路径
            searchPath = path.join(process.cwd(), searchPath);
        }
        
        // 检查目录是否存在
        if (!fs.existsSync(searchPath)) {
            return { status: 404, data: `错误：目录"${searchPath}"不存在` };
        }
        
        // 从配置文件中获取上下文行数
        const contextLines = config.tools.search_files && config.tools.search_files.contextLines ? 
            config.tools.search_files.contextLines : 20;
        
        // 递归搜索目录中的文件
        const results = searchFilesInDirectory(searchPath, regex, filePattern, contextLines);
        
        if (results.length > 0) {
            return { status: 200, data: `在目录"${searchPath}"中找到以下匹配项:\n${results.join('\n\n')}` };
        } else {
            return { status: 200, data: `在目录"${searchPath}"中未找到匹配"${regexStr}"的内容` };
        }
    } catch (error) {
        return { status: 500, data: `执行search_files工具时出错: ${error.message}` };
    }
}

// 在目录中递归搜索文件
function searchFilesInDirectory(dirPath, regex, filePattern, contextLines) {
    const results = [];
    
    function searchRecursive(currentPath) {
        const items = fs.readdirSync(currentPath);
        
        for (const item of items) {
            const itemPath = path.join(currentPath, item);
            const stat = fs.statSync(itemPath);
            
            if (stat.isDirectory()) {
                // 递归搜索子目录
                searchRecursive(itemPath);
            } else if (stat.isFile()) {
                // 检查文件模式是否匹配
                if (filePattern && !minimatch(item, filePattern)) {
                    continue;
                }
                
                // 读取文件内容并搜索
                try {
                    const content = fs.readFileSync(itemPath, 'utf8');
                    const lines = content.split('\n');
                    
                    // 存储匹配项的行号
                    const matchLines = [];
                    for (let i = 0; i < lines.length; i++) {
                        if (regex.test(lines[i])) {
                            matchLines.push(i);
                        }
                    }
                    
                    if (matchLines.length > 0) {
                        // 合并相邻的匹配区域（间隔contextLines行以内）
                        const mergedRanges = [];
                        let currentRange = {
                            start: Math.max(0, matchLines[0] - contextLines),
                            end: Math.min(lines.length - 1, matchLines[0] + contextLines)
                        };
                        
                        for (let i = 1; i < matchLines.length; i++) {
                            const line = matchLines[i];
                            const rangeStart = Math.max(0, line - contextLines);
                            const rangeEnd = Math.min(lines.length - 1, line + contextLines);
                            
                            // 如果当前范围与前一个范围重叠或间隔小于contextLines行，则合并
                            if (rangeStart <= currentRange.end + contextLines) {
                                currentRange.end = Math.max(currentRange.end, rangeEnd);
                            } else {
                                // 否则，将当前范围添加到结果中，并开始新的范围
                                mergedRanges.push(currentRange);
                                currentRange = {
                                    start: rangeStart,
                                    end: rangeEnd
                                };
                            }
                        }
                        
                        // 添加最后一个范围
                        mergedRanges.push(currentRange);
                        
                        // 为每个合并后的范围生成上下文
                        for (const range of mergedRanges) {
                            const contextLines = lines.slice(range.start, range.end + 1);
                            const context = contextLines
                                .map((line, idx) => `${range.start + idx + 1}: ${line}`)
                                .filter(line => {
                                    // 过滤掉只包含行号和空内容的行
                                    const hasContent = line.split(': ')[1]?.trim().length > 0;
                                    return hasContent;
                                })
                                .join('\n');

                            if (context.trim()) {
                                results.push(`文件: ${itemPath}:${range.start + 1}-${range.end + 1}\n${context}`);
                            }
                        }
                    }
                } catch (error) {
                    // 忽略无法读取的文件
                }
            }
        }
    }
    
    searchRecursive(dirPath);
    return results;
}

// 改进的glob模式匹配函数
function minimatch(filename, pattern) {
    if (!pattern) return true;
    
    // 处理常见的glob模式
    if (pattern === '*') return true;
    
    // 处理*.extension模式
    if (pattern.startsWith('*.')) {
        const extension = pattern.substring(2);
        return filename.endsWith('.' + extension);
    }
    
    // 处理更复杂的模式，如 *test*.js
    if (pattern.includes('*')) {
        // 将glob模式转换为正则表达式
        const regexPattern = pattern.replace(/\./g, '\\.').replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`);
        return regex.test(filename);
    }
    
    // 精确匹配
    return filename === pattern;
}

// 导出函数供测试使用
module.exports = {
    parseToolCall,
    parseSearchFilesInfo,
    getDocumentPathContents,
    manageContext,
    estimateTokens,
    listFiles,
    readFile,
    searchFiles,
    searchDocuments,
    executeMcpTool,
    executeFullIndexSearch,
    fullIndexSearch,
    initializeFullIndex,
    fetchWebPage
};

// 获取documentPath目录下所有文件和目录的函数
function getDocumentPathContents() {
    try {
        // 获取documentPath目录下的两层文件和目录结构
        function getDirectoryStructure(dirPath, depth = 0, prefix = '') {
            let result = '';
            const items = fs.readdirSync(dirPath);
            
            // 分离目录和文件
            const directories = [];
            const files = [];
            
            for (const item of items) {
                const itemPath = path.join(dirPath, item);
                const stat = fs.statSync(itemPath);
                
                if (stat.isDirectory()) {
                    directories.push(item);
                } else if (stat.isFile()) {
                    files.push(item);
                }
            }
            
            // 添加目录（最多两层）
            directories.sort().forEach(dir => {
                const dirPathFull = path.join(dirPath, dir);
                result += `${prefix}- ${dir}/\n`;
                // 如果深度小于2，递归获取子目录结构
                if (depth < 1) {
                    result += getDirectoryStructure(dirPathFull, depth + 1, prefix + '  ');
                }
            });
            
            // 添加文件
            files.sort().forEach(file => {
                result += `${prefix}- ${file}\n`;
            });
            
            return result;
        }
        
        // 构建环境详情内容
        let environmentDetail = `<environment_detail>\n`;
        
        // 添加产品简介（如果在配置文件中定义）
        if (config.productDescriptionFile) {
            try {
                const productDescription = fs.readFileSync(path.join(__dirname, config.productDescriptionFile), 'utf8');
                environmentDetail += `# Product Description\n`;
                environmentDetail += `${productDescription}\n\n`;
            } catch (error) {
                console.error(`无法读取产品简介文件: ${error.message}`);
            }
        }
        
        environmentDetail += `# DocumentPath Contents\n`;
        environmentDetail += `DocumentPath: ${documentPath}\n\n`;
        
        // 获取两层目录结构
        const directoryStructure = getDirectoryStructure(documentPath);
        if (directoryStructure) {
            environmentDetail += `## Directory Structure\n`;
            environmentDetail += directoryStructure;
            environmentDetail += `\n`;
        }
        
        environmentDetail += `</environment_detail>`;
        
        return environmentDetail;
    } catch (error) {
        // 如果出现错误，返回空的环境详情
        return `<environment_detail>\n# DocumentPath Contents\nDocumentPath: ${documentPath}\n\n## Error\n无法读取目录内容: ${error.message}\n\n</environment_detail>`;
    }
}

// 程序启动时初始化全文索引
if (require.main === module) {
    // 只有在直接运行此文件时才初始化
    initializeFullIndex().catch(error => {
        console.error('全文索引初始化失败:', error);
        process.exit(1);
    });
}
