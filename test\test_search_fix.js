const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');

async function testSearchFix() {
    console.log('测试修复后的搜索功能...\n');
    
    try {
        // 初始化全文索引引擎
        await initializeFullIndex();
        console.log('全文索引引擎初始化完成\n');
        
        // 测试原始失败的查询
        console.log('1. 测试原始查询: "漏洞修复 扫描 同步" in "终端安全"');
        const result1 = await executeFullIndexSearch({
            keywords: ["漏洞修复 扫描 同步"],
            path: "终端安全"
        });
        
        if (result1.status === 200) {
            console.log('✓ 搜索成功！');
            // 检查是否找到了目标内容
            const hasTarget = result1.data.includes('【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法');
            console.log(`✓ 找到目标内容: ${hasTarget ? '是' : '否'}`);
            
            if (hasTarget) {
                console.log('✓ 修复成功！搜索现在能正确找到目标内容了。');
            }
        } else {
            console.log('✗ 搜索仍然失败');
            console.log('结果:', result1);
        }
        
        console.log('\n2. 测试其他查询:');
        
        // 测试其他查询
        const otherQueries = [
            "Android 管控",
            "新天擎 补丁管理",
            "病毒 查杀"
        ];
        
        for (const query of otherQueries) {
            console.log(`\n   测试: "${query}"`);
            const result = await executeFullIndexSearch({
                keywords: [query],
                path: "终端安全"
            });
            
            if (result.status === 200) {
                console.log(`   ✓ 搜索成功，找到 ${result.data.split('\n').length} 行结果`);
            } else {
                console.log(`   ✗ 搜索失败: ${result.data}`);
            }
        }
        
    } catch (error) {
        console.error('测试过程中出错:', error);
    }
}

// 运行测试
testSearchFix();
