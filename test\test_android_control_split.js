const { fullIndexSearch } = require('../chat-agent.js');

// 导入indexer.js中的CNFullText类来测试分词逻辑
const { CNFullText } = require('../indexer.js');

async function testTokenization() {
    console.log('Testing tokenization logic for specific text:');
    console.log('==============================================');

    // 创建一个CNFullText实例来测试分词
    const indexer = new CNFullText();

    // 测试文本：问题描述
    const testText = "问题:【天机trustspace】【客户端】Android端强管控功能不生效";
    console.log('原始文本:', testText);
    console.log('');

    // 1. 测试normalize处理
    const normalizedText = indexer._normalize(testText);
    console.log('1. Normalize处理后:', normalizedText);
    console.log('');

    // 2. 测试分词处理
    const tokens = indexer._tokenize(normalizedText);
    console.log('2. 分词结果:');
    tokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');
    console.log(`总共分出 ${tokens.length} 个词`);
    console.log('');

    // 3. 测试简单分词逻辑（fallback方法）
    console.log('3. 简单分词逻辑（fallback）:');
    const simpleTokens = indexer._simpleTokenize(normalizedText);
    console.log('简单分词结果:');
    simpleTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');
    console.log(`简单分词总共分出 ${simpleTokens.length} 个词`);
    console.log('');

    // 4. 测试答案文本的分词
    const answerText = "答案:https://kb.qianxin.com/detail/d73d0967154";
    console.log('4. 答案文本分词测试:');
    console.log('原始答案文本:', answerText);
    const answerNormalized = indexer._normalize(answerText);
    console.log('Normalize后:', answerNormalized);
    const answerTokens = indexer._tokenize(answerNormalized);
    console.log('答案分词结果:');
    answerTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');

    // 5. 测试完整文本的分词
    const fullText = testText + "\n" + answerText;
    console.log('5. 完整文本分词测试:');
    console.log('完整文本:', fullText);
    const fullNormalized = indexer._normalize(fullText);
    console.log('Normalize后:', fullNormalized);
    const fullTokens = indexer._tokenize(fullNormalized);
    console.log('完整文本分词结果:');
    fullTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');
    console.log(`完整文本总共分出 ${fullTokens.length} 个词`);
}

async function testAndroidControlSplit() {
    console.log('\n\nTesting Android 管控 split search in 终端安全 path:');
    console.log('================================================');

    try {
        // 测试搜索"终端安全"路径下的"Android 管控"（应该被视为两个独立关键词）
        console.log('Test: Searching for "Android 管控" in "终端安全" path');
        console.log('This should be treated as two separate keywords: "Android" and "管控"');
        const result = await fullIndexSearch('Android 管控', '终端安全');
        console.log('Results count:', result.length);
        console.log('Results:');
        result.forEach((item, index) => {
            console.log(`${index + 1}. ${item}`);
        });
        console.log('');

        // 检查是否找到了天擎_faq.txt.003文件
        const foundFaq003 = result.some(item => item.includes('天擎_faq.txt.003'));
        console.log('Found 天擎_faq.txt.003:', foundFaq003 ? 'YES' : 'NO');
        console.log('');

        // 测试搜索"强管控"
        console.log('Test: Searching for "强管控" in "终端安全" path');
        const result2 = await fullIndexSearch('强管控', '终端安全');
        console.log('Results count:', result2.length);
        console.log('Results:');
        result2.forEach((item, index) => {
            console.log(`${index + 1}. ${item}`);
        });
        console.log('');

        // 检查是否找到了包含"强管控"的文件
        if (result2.length > 0) {
            console.log('Files containing "强管控":');
            result2.forEach((item, index) => {
                const lines = item.split('\n');
                const fileName = lines[0].replace('文件: ', '');
                console.log(`  ${index + 1}. ${fileName}`);
            });
        }
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Run the tests
async function runAllTests() {
    // 首先测试分词逻辑
    await testTokenization();

    // 然后测试搜索功能
    await testAndroidControlSplit();
}

runAllTests();
