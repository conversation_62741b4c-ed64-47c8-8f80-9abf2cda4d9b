const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');
const { CNFullText } = require('../indexer.js');

async function debugSearchFailure() {
    console.log('调试搜索失败问题...\n');
    
    try {
        // 初始化全文索引引擎
        await initializeFullIndex();
        console.log('全文索引引擎初始化完成\n');
        
        // 1. 测试原始搜索
        console.log('1. 测试原始搜索: "漏洞修复 扫描 同步" in "终端安全"');
        const result1 = await executeFullIndexSearch({
            keywords: ["漏洞修复 扫描 同步"],
            path: "终端安全"
        });
        console.log('结果1:', result1);
        console.log('');
        
        // 2. 测试拆分关键词搜索
        console.log('2. 测试拆分关键词搜索: ["漏洞修复", "扫描", "同步"] in "终端安全"');
        const result2 = await executeFullIndexSearch({
            keywords: ["漏洞修复", "扫描", "同步"],
            path: "终端安全"
        });
        console.log('结果2:', result2);
        console.log('');
        
        // 3. 测试单个关键词
        console.log('3. 测试单个关键词: "漏洞修复" in "终端安全"');
        const result3 = await executeFullIndexSearch({
            keywords: ["漏洞修复"],
            path: "终端安全"
        });
        console.log('结果3:', result3);
        console.log('');
        
        // 4. 测试另一个关键词
        console.log('4. 测试另一个关键词: "扫描" in "终端安全"');
        const result4 = await executeFullIndexSearch({
            keywords: ["扫描"],
            path: "终端安全"
        });
        console.log('结果4:', result4);
        console.log('');
        
        // 5. 测试"新天擎"
        console.log('5. 测试"新天擎" in "终端安全"');
        const result5 = await executeFullIndexSearch({
            keywords: ["新天擎"],
            path: "终端安全"
        });
        console.log('结果5:', result5);
        console.log('');
        
        // 6. 测试"补丁管理"
        console.log('6. 测试"补丁管理" in "终端安全"');
        const result6 = await executeFullIndexSearch({
            keywords: ["补丁管理"],
            path: "终端安全"
        });
        console.log('结果6:', result6);
        console.log('');
        
        // 7. 测试分词逻辑
        console.log('7. 测试分词逻辑:');
        const indexer = new CNFullText();
        const testText = "【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法";
        const tokens = indexer._tokenize(testText);
        console.log('原文:', testText);
        console.log('分词结果:', tokens);
        console.log('');
        
        // 8. 测试OR搜索
        console.log('8. 测试OR搜索: "漏洞修复" OR "扫描" in "终端安全"');
        const result8 = await executeFullIndexSearch({
            keywords: ["漏洞修复"],
            path: "终端安全"
        });
        console.log('结果8:', result8);
        
    } catch (error) {
        console.error('调试过程中出错:', error);
    }
}

// 运行调试
debugSearchFailure();
