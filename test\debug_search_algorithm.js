const { CNFullText } = require('../indexer.js');

function debugSearchAlgorithm() {
    console.log('调试搜索算法问题...\n');
    
    // 创建一个简单的测试索引，使用正确的字段配置
    const indexer = new CNFullText({
        textField: 'text',  // 使用text字段
        idField: 'id'       // 使用id字段
    });

    // 测试文档
    const testDoc = {
        id: 'test1',
        text: '问题:【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法'
    };
    
    console.log('1. 原始文档:');
    console.log(testDoc.text);
    console.log('');
    
    // 建立索引
    indexer.build([testDoc]);
    
    // 测试查询分词
    const queries = [
        '漏洞修复',
        '扫描', 
        '同步',
        '漏洞修复 扫描',
        '漏洞修复 扫描 同步'
    ];
    
    queries.forEach(query => {
        console.log(`2. 查询"${query}"的分词结果:`);
        const normalizedQuery = indexer._normalize(query);
        console.log(`   Normalized: "${normalizedQuery}"`);
        const queryTokens = indexer._tokenize(normalizedQuery);
        console.log(`   Tokens: [${queryTokens.map(t => `"${t}"`).join(', ')}]`);
        
        // 检查每个token是否在倒排索引中存在
        console.log('   倒排索引检查:');
        queryTokens.forEach(token => {
            const exists = indexer.inverted.has(token);
            console.log(`     "${token}": ${exists ? '存在' : '不存在'}`);
            if (exists) {
                const entry = indexer.inverted.get(token);
                console.log(`       文档数: ${entry.df}, 位置: ${JSON.stringify(entry.postings)}`);
            }
        });
        
        // 执行搜索
        console.log('   搜索结果:');
        try {
            const results = indexer.search(query, { requireAll: true, topK: 10 });
            console.log(`     找到 ${results.length} 个结果`);
            results.forEach((result, idx) => {
                console.log(`     ${idx + 1}. ID: ${result.id}, Score: ${result.score}, Coverage: ${result.coverage}`);
            });
        } catch (error) {
            console.log(`     搜索出错: ${error.message}`);
        }
        console.log('');
    });
    
    // 显示完整的倒排索引
    console.log('3. 完整倒排索引:');
    for (const [term, entry] of indexer.inverted.entries()) {
        console.log(`   "${term}": df=${entry.df}, postings=${JSON.stringify(entry.postings)}`);
    }
    console.log('');
    
    // 手动模拟AND逻辑
    console.log('4. 手动模拟AND逻辑 - "漏洞修复 扫描 同步":');
    const testQuery = '漏洞修复 扫描 同步';
    const testTokens = indexer._tokenize(indexer._normalize(testQuery));
    console.log(`   查询tokens: [${testTokens.map(t => `"${t}"`).join(', ')}]`);
    
    const postingLists = [];
    for (const term of testTokens) {
        const entry = indexer.inverted.get(term);
        if (!entry) {
            console.log(`   词"${term}"不存在于倒排索引中 - AND逻辑应该返回空结果`);
            return;
        }
        postingLists.push({ term, entry });
        console.log(`   词"${term}"存在: ${JSON.stringify(entry.postings)}`);
    }
    
    console.log(`   所有词都存在，postingLists长度: ${postingLists.length}`);
    
    // 计算交集
    const sets = postingLists.map(p => new Set(p.entry.postings.map(x => x.id)));
    console.log(`   各词的文档ID集合:`);
    sets.forEach((set, idx) => {
        console.log(`     词"${testTokens[idx]}": [${[...set].join(', ')}]`);
    });
    
    let candidateIds = sets[0];
    console.log(`   初始候选集: [${[...candidateIds].join(', ')}]`);
    
    for (let i = 1; i < sets.length; i++) {
        const beforeSize = candidateIds.size;
        candidateIds = new Set([...candidateIds].filter(x => sets[i].has(x)));
        console.log(`   与词"${testTokens[i]}"求交集后: [${[...candidateIds].join(', ')}] (${beforeSize} -> ${candidateIds.size})`);
        if (candidateIds.size === 0) {
            console.log('   交集为空，返回无结果');
            return;
        }
    }
    
    console.log(`   最终候选文档: [${[...candidateIds].join(', ')}]`);
}

// 运行调试
debugSearchAlgorithm();
