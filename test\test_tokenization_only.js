// 只测试分词逻辑，不依赖全文索引
const { CNFullText } = require('../indexer.js');

function testTokenization() {
    console.log('Testing tokenization logic for specific text:');
    console.log('==============================================');
    
    // 创建一个CNFullText实例来测试分词
    const indexer = new CNFullText();
    
    // 测试文本：问题描述
    const testText = "问题:【天机trustspace】【客户端】Android端强管控功能不生效";
    console.log('原始文本:', testText);
    console.log('');
    
    // 1. 测试normalize处理
    const normalizedText = indexer._normalize(testText);
    console.log('1. Normalize处理后:', normalizedText);
    console.log('');
    
    // 2. 测试分词处理
    const tokens = indexer._tokenize(normalizedText);
    console.log('2. 分词结果:');
    tokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');
    console.log(`总共分出 ${tokens.length} 个词`);
    console.log('');
    
    // 3. 测试简单分词逻辑（fallback方法）
    console.log('3. 简单分词逻辑（fallback）:');
    const simpleTokens = indexer._simpleTokenize(normalizedText);
    console.log('简单分词结果:');
    simpleTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');
    console.log(`简单分词总共分出 ${simpleTokens.length} 个词`);
    console.log('');
    
    // 4. 测试答案文本的分词
    const answerText = "答案:https://kb.qianxin.com/detail/d73d0967154";
    console.log('4. 答案文本分词测试:');
    console.log('原始答案文本:', answerText);
    const answerNormalized = indexer._normalize(answerText);
    console.log('Normalize后:', answerNormalized);
    const answerTokens = indexer._tokenize(answerNormalized);
    console.log('答案分词结果:');
    answerTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');
    
    // 5. 测试完整文本的分词
    const fullText = testText + "\n" + answerText;
    console.log('5. 完整文本分词测试:');
    console.log('完整文本:', fullText);
    const fullNormalized = indexer._normalize(fullText);
    console.log('Normalize后:', fullNormalized);
    const fullTokens = indexer._tokenize(fullNormalized);
    console.log('完整文本分词结果:');
    fullTokens.forEach((token, index) => {
        console.log(`   ${index + 1}. "${token}"`);
    });
    console.log('');
    console.log(`完整文本总共分出 ${fullTokens.length} 个词`);
    
    // 6. 测试特定关键词的分词
    console.log('\n6. 测试特定关键词分词:');
    const keywords = ['Android', '管控', '强管控', 'Android端', '天机', 'trustspace'];
    keywords.forEach(keyword => {
        const keywordTokens = indexer._tokenize(keyword);
        console.log(`"${keyword}" -> [${keywordTokens.map(t => `"${t}"`).join(', ')}]`);
    });
}

// Run the test
testTokenization();
