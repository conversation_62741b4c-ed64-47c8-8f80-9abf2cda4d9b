const { CNFullText } = require('../indexer.js');

function testConsistentTokenization() {
    console.log('测试分词一致性问题...\n');
    
    const indexer = new CNFullText();
    
    // 测试文档内容（模拟索引时的文本）
    const documentText = "问题:【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法";
    console.log('1. 文档文本（建索引时）:');
    console.log(`   原文: ${documentText}`);
    
    const docNormalized = indexer._normalize(documentText);
    console.log(`   Normalize后: ${docNormalized}`);
    
    const docTokens = indexer._tokenize(docNormalized);
    console.log(`   分词结果:`);
    docTokens.forEach((token, idx) => {
        console.log(`     ${idx + 1}. "${token}"`);
    });
    console.log(`   总词数: ${docTokens.length}\n`);
    
    // 测试查询文本（模拟检索时的文本）
    const queryText = "漏洞修复 扫描 同步";
    console.log('2. 查询文本（检索时）:');
    console.log(`   原文: ${queryText}`);
    
    const queryNormalized = indexer._normalize(queryText);
    console.log(`   Normalize后: ${queryNormalized}`);
    
    const queryTokens = indexer._tokenize(queryNormalized);
    console.log(`   分词结果:`);
    queryTokens.forEach((token, idx) => {
        console.log(`     ${idx + 1}. "${token}"`);
    });
    console.log(`   总词数: ${queryTokens.length}\n`);
    
    // 检查匹配情况
    console.log('3. 匹配检查:');
    console.log('   查询词在文档分词中的匹配情况:');
    
    let allMatched = true;
    queryTokens.forEach(queryToken => {
        const found = docTokens.includes(queryToken);
        console.log(`     "${queryToken}": ${found ? '✓ 匹配' : '✗ 不匹配'}`);
        if (!found) allMatched = false;
    });
    
    console.log(`\n   AND逻辑结果: ${allMatched ? '✓ 所有词都匹配，搜索应该成功' : '✗ 部分词不匹配，搜索会失败'}`);
    
    // 测试其他查询
    console.log('\n4. 测试其他查询:');
    const otherQueries = [
        "Android 管控",
        "新天擎 补丁管理",
        "病毒 查杀"
    ];
    
    otherQueries.forEach((query, idx) => {
        console.log(`\n   查询 ${idx + 1}: "${query}"`);
        const qTokens = indexer._tokenize(indexer._normalize(query));
        console.log(`   分词: [${qTokens.map(t => `"${t}"`).join(', ')}]`);
        
        const matches = qTokens.every(token => docTokens.includes(token));
        console.log(`   与文档匹配: ${matches ? '✓' : '✗'}`);
    });
}

// 运行测试
testConsistentTokenization();
