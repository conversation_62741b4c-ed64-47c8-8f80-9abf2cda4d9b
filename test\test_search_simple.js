const { executeFullIndexSearch } = require('../chat-agent.js');

async function testSearchSimple() {
    console.log('测试搜索功能（使用已有索引）...\n');
    
    try {
        // 测试长查询
        console.log('测试查询: "扫描同步终端的漏洞修复情况的方法" in "终端安全"');
        const result = await executeFullIndexSearch({
            keywords: ["扫描同步终端的漏洞修复情况的方法"],
            path: "终端安全"
        });
        
        if (result.status === 200) {
            console.log('✅ 搜索成功！');
            
            // 显示搜索结果的前几行
            const lines = result.data.split('\n').filter(line => line.trim());
            console.log(`\n搜索结果（共${lines.length}行，显示前15行）:`);
            lines.slice(0, 15).forEach((line, idx) => {
                console.log(`${idx + 1}. ${line}`);
            });
            
            // 检查是否找到了目标内容
            const targetPatterns = [
                '【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法',
                '新天擎',
                '补丁管理',
                '漏洞修复',
                '扫描同步'
            ];
            
            console.log('\n内容检查:');
            targetPatterns.forEach(pattern => {
                const found = result.data.includes(pattern);
                console.log(`  "${pattern}": ${found ? '✓' : '✗'}`);
            });
            
        } else {
            console.log('❌ 搜索失败');
            console.log('错误:', result.data);
        }
        
    } catch (error) {
        console.error('测试过程中出错:', error.message);
    }
}

// 运行测试
testSearchSimple();
