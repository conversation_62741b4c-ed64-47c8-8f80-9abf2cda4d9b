const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');

async function testFinalSearch() {
    console.log('测试最终修复后的搜索功能...\n');
    
    try {
        // 初始化全文索引引擎
        await initializeFullIndex();
        console.log('全文索引引擎初始化完成\n');
        
        // 测试原始失败的查询
        console.log('测试原始查询: "漏洞修复 扫描 同步" in "终端安全"');
        const result = await executeFullIndexSearch({
            keywords: ["漏洞修复 扫描 同步"],
            path: "终端安全"
        });
        
        if (result.status === 200) {
            console.log('✅ 搜索成功！');

            // 显示搜索结果的前几行
            const lines = result.data.split('\n');
            console.log(`\n搜索结果（前10行）:`);
            lines.slice(0, 10).forEach((line, idx) => {
                console.log(`${idx + 1}. ${line}`);
            });

            // 检查是否找到了目标内容
            const hasTarget = result.data.includes('【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法');
            console.log(`\n✅ 找到目标内容: ${hasTarget ? '是' : '否'}`);

            // 检查是否包含相关关键词
            const hasKeywords = {
                '新天擎': result.data.includes('新天擎'),
                '补丁管理': result.data.includes('补丁管理'),
                '漏洞修复': result.data.includes('漏洞修复'),
                '扫描': result.data.includes('扫描'),
                '同步': result.data.includes('同步')
            };
            console.log('\n关键词检查:');
            Object.entries(hasKeywords).forEach(([key, found]) => {
                console.log(`  ${key}: ${found ? '✓' : '✗'}`);
            });

            if (hasTarget) {
                console.log('🎉 修复完全成功！搜索现在能正确找到目标内容了。');

                // 显示找到的内容片段
                const targetLine = lines.find(line => line.includes('【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法'));
                if (targetLine) {
                    console.log('\n找到的目标内容:');
                    console.log(`"${targetLine.trim()}"`);
                }
            }
        } else {
            console.log('❌ 搜索仍然失败');
            console.log('结果:', result);
        }
        
    } catch (error) {
        console.error('测试过程中出错:', error);
    }
}

// 运行测试
testFinalSearch();
