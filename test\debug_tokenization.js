const { CNFullText } = require('../indexer.js');

function debugTokenization() {
    console.log('验证分词一致性...\n');

    const indexer = new CNFullText();

    // 测试文档文本（建索引时）
    const docText = "问题:【新天擎】【补丁管理】扫描同步终端的漏洞修复情况的方法";
    console.log('1. 文档文本（建索引时）:');
    console.log(`   原文: ${docText}`);

    const docNormalized = indexer._normalize(docText);
    console.log(`   Normalize后: "${docNormalized}"`);

    const docTokens = indexer._tokenize(docNormalized);
    console.log(`   完整分词结果:`);
    docTokens.forEach((token, idx) => {
        console.log(`     ${idx + 1}. "${token}"`);
    });

    console.log(`\n   关键词检查:`);
    const keyWords = ['漏洞修复', '扫描', '同步', '新天擎', '补丁管理'];
    keyWords.forEach(word => {
        const found = docTokens.includes(word);
        console.log(`     "${word}": ${found ? '✓' : '✗'}`);
    });

    // 测试查询文本（检索时）
    console.log('\n2. 查询文本（检索时）:');
    const queries = [
        "漏洞修复 扫描 同步",
        "扫描同步终端的漏洞修复情况的方法",
        "新天擎 补丁管理",
        "Android 管控"
    ];

    queries.forEach((query, idx) => {
        console.log(`\n   查询 ${idx + 1}: "${query}"`);

        const queryNormalized = indexer._normalize(query);
        console.log(`   Normalize后: "${queryNormalized}"`);

        const queryTokens = indexer._tokenize(queryNormalized);
        console.log(`   分词结果: [${queryTokens.map(t => `"${t}"`).join(', ')}]`);

        // 检查是否所有查询词都在文档分词中
        const allMatch = queryTokens.every(token => docTokens.includes(token));
        console.log(`   与文档匹配: ${allMatch ? '✓ 所有词都匹配' : '✗ 部分词不匹配'}`);
    });
}

// 运行调试
debugTokenization();
